目标：生成一个手机端网页（PWA）的 AI 聊天应用 UI，全流程包含：1）密码门禁页 → 2）模型选择与 API Key 管理页 → 3）聊天主界面（含历史/清空/加载） 。视觉需现代、精致、轻拟物+玻璃霜化风格，可深浅色自适应，适配 iPhone 14/15 视口与安全区域。

品牌与风格：

气质：干净、科技、可信任；圆角 20px、8pt 栅格、柔和阴影（blur 24、y=8、spread=0）。

调色板：主色 #4C7DFF，辅色 #22C55E、强调 #F59E0B；浅色背景 #0F1420→#121826 渐变（深色）或 #F7F8FB（浅色）。

字体：SF Pro / Inter；标题 20–24、正文 14–16、说明 12；行高 1.4–1.6。

图标：线性图标（lucide 风格）。微交互 180–220ms，减速曲线。

信息架构与页面：

密码门禁页（首次打开）

顶部品牌位（简洁图标 + 文案「欢迎使用 SuperChat」），说明文案：“请输入访问密码”。

输入框：• 支持显示/隐藏密码；• 错误态（抖动+红色辅助文案）。

主按钮「进入」；次要链接「忘记密码？」（可选）。

视觉：半透明卡片 + 毛玻璃背景插画（蓝紫色渐变、细网格）。

模型选择 & API Key 管理页

模型选择区：卡片栅格（2 列）：

每张卡包含：模型名（如「gpt‑4o‑mini」「deepseek‑r1」「llama‑3.1‑8b」等占位）、概述（20–30 字）、标签（⚡速度 / 💎质量 / 💰成本）、估算延迟徽章。

交互：单选（突出当前选中卡片蓝色描边 + 轻微浮起）。

聊天历史区：

列表项：标题（自动生成，如「产品需求讨论」）、时间、模型徽标；右滑出现「加载历史」「删除」。

顶部工具：加载历史、清空历史（二次确认对话框）。

API Key 管理区：

当前 Key：以占位文案展示：sk-****************************（禁止展示真实密钥）。

按钮：生成新 Key（生成后出现一次性弹窗 +「复制 Key」「仅本次可见」说明）、复制 Key、撤销当前 Key（可选）。

说明：安全提示与最小权限原则。

页面底部主按钮：「进入聊天」。

聊天主界面

头部栏：左侧返回/侧边栏；中部模型标签（可下拉切换）；右侧「设置」入口（含主题、模型、API Key、清缓存）。

消息区（气泡式）：

助手气泡：玻璃感卡片、左侧小头像（✨）、多行文本、代码块样式（等宽字体 + 轻边框 + 复制按钮）。

用户气泡：强调色背景，右对齐；时间戳在底部 12 号灰色。

消息工具：复制、重新生成、赞/踩、删除。

骨架屏：思考中显示三段式占位条。

历史管理：上滑出现历史抽屉；支持搜索、固定对话、清空全部（二次确认）。

输入区：

多行输入框（自动增高，最高 4 行），占位符「向 AI 提问…」。

左侧：附件（可选）、清空输入；右侧：发送主按钮（按下波纹反馈）。

提示条：Shift+Enter 换行（移动端提示可省略）。

状态与无障碍：

错误、空、加载、无网络（离线提示条）；

触控目标≥44×44；对比度≥WCAG AA；语义标签与辅助文案。

输出要求：

生成全流程 3 个页面 + 状态样例（骨架、错误、确认弹窗），提供移动端预览帧（iPhone 15 Pro）。

交付包括：组件样式变量（颜色、圆角、阴影、间距）、图标清单、交互动效说明（时长/曲线）。